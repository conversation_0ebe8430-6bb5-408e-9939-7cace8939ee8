{% extends "base.html" %}

{% block title %}{{ exam.exam_name }} Documents | PYQs Admin | GPT Sir{% endblock %}

{% block head %}
<style>
  .pyqs-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .pyqs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e7eb;
  }

  .pyqs-header h1 {
    font-size: 1.775rem;
    font-weight: 700;
    color: #111827;
    margin: 0;
  }

  .back-btn {
    background-color: #f3f4f6;
    color: #374151;
    padding: 12px 20px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s;
    border: 1px solid #e5e7eb;
  }

  .back-btn:hover {
    background-color: #e5e7eb;
    transform: translateY(-1px);
  }

  .upload-form {
    background-color: white;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    padding: 24px;
    margin-bottom: 32px;
  }

  .form-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: #111827;
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
  }

  .form-group {
    margin-bottom: 16px;
  }

  .form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    font-size: 0.875rem;
    color: #374151;
  }

  .form-control {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.875rem;
    background-color: #f9fafb;
    transition: border-color 0.2s, background-color 0.2s;
  }

  .form-control:focus {
    outline: none;
    border-color: #10b981;
    background-color: white;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
  }

  .file-input-group {
    margin-bottom: 24px;
  }

  .file-input-label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    font-size: 0.875rem;
    color: #374151;
  }

  .file-input {
    width: 97%;
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    background-color: #f9fafb;
    font-size: 0.875rem;
    transition: border-color 0.2s;
  }

  .file-input:focus {
    outline: none;
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
  }

  .submit-btn {
    background-color: #10b981;
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .submit-btn:hover {
    background-color: #059669;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  }

  .documents-list {
    background-color: white;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    overflow: hidden;
  }

  .documents-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7eb;
    background-color: #f9fafb;
  }

  .documents-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
    margin: 0;
  }

  .document-item {
    padding: 20px 24px;
    border-bottom: 1px solid #f3f4f6;
    transition: background-color 0.2s;
  }

  .document-item:last-child {
    border-bottom: none;
  }

  .document-item:hover {
    background-color: #f9fafb;
  }

  .document-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;
  }

  .document-info {
    flex: 1;
  }

  .document-name {
    font-weight: 600;
    font-size: 1rem;
    color: #111827;
    margin-bottom: 6px;
  }

  .document-meta {
    color: #6b7280;
    font-size: 0.875rem;
  }

  .document-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .action-btn {
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.2s;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    white-space: nowrap;
  }

  .view-btn {
    background-color: #3b82f6;
    color: white;
    border: 1px solid #3b82f6;
  }

  .view-btn:hover {
    background-color: #2563eb;
    border-color: #2563eb;
  }

  .extract-btn {
    background-color: #10b981;
    color: white;
    border: 1px solid #10b981;
  }

  .extract-btn:hover {
    background-color: #059669;
    border-color: #059669;
  }

  .edit-btn {
    background-color: #f59e0b;
    color: white;
    border: 1px solid #f59e0b;
  }

  .edit-btn:hover {
    background-color: #d97706;
    border-color: #d97706;
  }

  .delete-btn {
    background-color: #ef4444;
    color: white;
    border: 1px solid #ef4444;
  }

  .delete-btn:hover {
    background-color: #dc2626;
    border-color: #dc2626;
  }

  .pagination {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 24px;
    padding: 20px 24px;
  }

  .pagination-item {
    display: inline-block;
  }

  .pagination-link {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
    padding: 0 12px;
    border-radius: 8px;
    text-decoration: none;
    color: #6b7280;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.2s;
    border: 1px solid #e5e7eb;
    background-color: white;
  }

  .pagination-link:hover {
    background-color: #f9fafb;
    border-color: #d1d5db;
    color: #374151;
  }

  .pagination-link.active {
    background-color: #10b981;
    color: white;
    border-color: #10b981;
  }

  .pagination-link.disabled {
    color: #d1d5db;
    pointer-events: none;
    background-color: #f9fafb;
  }

  .error-message {
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 0.875rem;
  }

  .no-documents {
    padding: 48px 24px;
    text-align: center;
    color: #6b7280;
    font-size: 1rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Modern Loader Styles */
  .loader-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 30px;
    background-color: #f8f9fa;
    border-radius: 10px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  }

  .loader {
    width: 60px;
    height: 60px;
    border: 6px solid #f3f3f3;
    border-top: 6px solid #10b981;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loader-text {
    flex: 1;
    max-width: 400px;
  }

  #loaderTitle {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
  }

  #loaderSubtitle {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
  }

  .progress-bar-container {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .progress-bar {
    flex: 1;
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
  }

  .progress-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #10b981, #059669);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
  }

  .progress-text {
    font-size: 12px;
    font-weight: bold;
    color: #10b981;
    min-width: 35px;
  }

  /* Progress Log Styles */
  #logContainer {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border: 1px solid #dee2e6;
    margin-top: 20px;
  }

  #logContainer h3 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  #timer {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white !important;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: bold;
    font-family: 'Courier New', monospace;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0% { box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3); }
    50% { box-shadow: 0 2px 15px rgba(16, 185, 129, 0.5); }
    100% { box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3); }
  }

  .progress-log {
    background: #ffffff;
    border-radius: 8px;
    padding: 15px;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.05);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  }

  .progress-log::-webkit-scrollbar {
    width: 8px;
  }

  .progress-log::-webkit-scrollbar-track {
    background: #f1f3f4;
    border-radius: 4px;
  }

  .progress-log::-webkit-scrollbar-thumb {
    background: #c1c8cd;
    border-radius: 4px;
  }

  .progress-log::-webkit-scrollbar-thumb:hover {
    background: #a8b2ba;
  }

  .log-entry {
    padding: 8px 12px;
    margin-bottom: 6px;
    border-radius: 6px;
    font-size: 14px;
    line-height: 1.4;
    position: relative;
    animation: slideIn 0.3s ease-out;
    border-left: 3px solid transparent;
    transition: all 0.2s ease;
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateX(-10px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .log-entry:hover {
    background-color: #f8f9fa;
    transform: translateX(2px);
  }

  .log-entry::before {
    content: "•";
    color: #6c757d;
    margin-right: 8px;
    font-weight: bold;
  }

  .log-entry::after {
    content: attr(data-time);
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 11px;
    color: #6c757d;
    opacity: 0.7;
  }

  .progress-log:empty::before {
    content: "No logs yet...";
    display: block;
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 20px;
  }

  @media (max-width: 768px) {
    .pyqs-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .form-grid {
      grid-template-columns: 1fr;
    }

    .document-content {
      flex-direction: column;
      gap: 16px;
    }

    .document-actions {
      justify-content: flex-start;
    }

    .action-btn {
      flex: 1;
      justify-content: center;
      min-width: 0;
    }
  }
</style>
{% endblock %}

{% block content %}
<div class="pyqs-container">
  <div class="pyqs-header">
    <h1>{{ exam.exam_name }}</h1>
    <a href="/pyqs_admin/exams" class="back-btn">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
        <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z"/>
      </svg>
      Back to Exams
    </a>
  </div>

  {% if error %}
    <div class="error-message">
      {{ error }}
    </div>
  {% endif %}

  <div class="upload-form">
    <h2 class="form-title">Upload Question Paper</h2>
    <form action="/pyqs_admin/exams/{{ exam.id }}/documents" method="post" enctype="multipart/form-data">
      <div class="form-grid">
        <div class="form-group">
          <label for="year">Year (required)</label>
          <input type="number" id="year" name="year" class="form-control" required min="1900" max="2100" value="{{ current_year }}">
        </div>
        <div class="form-group">
          <label for="month">Month (optional)</label>
          <select id="month" name="month" class="form-control">
            <option value="">Select Month</option>
            <option value="January">January</option>
            <option value="February">February</option>
            <option value="March">March</option>
            <option value="April">April</option>
            <option value="May">May</option>
            <option value="June">June</option>
            <option value="July">July</option>
            <option value="August">August</option>
            <option value="September">September</option>
            <option value="October">October</option>
            <option value="November">November</option>
            <option value="December">December</option>
          </select>
        </div>
        <div class="form-group">
          <label for="shift">Shift (optional)</label>
          <input type="text" id="shift" name="shift" class="form-control" placeholder="e.g., Morning, Evening, Shift 1">
        </div>
      </div>
      <div class="file-input-group">
        <label for="file" class="file-input-label">PDF File (required)</label>
        <input type="file" id="file" name="file" class="file-input" accept=".pdf" required>
      </div>
      <button type="submit" class="submit-btn">Upload Question Paper</button>
    </form>
  </div>

  <div class="documents-list">
    <div class="documents-header">
      <h2 class="documents-title">Uploaded Question Papers</h2>
    </div>

    {% if documents %}
      {% for document in documents %}
        <div class="document-item">
          <div class="document-content">
            <div class="document-info">
              <div class="document-name">
                {% if document.month and document.shift %}
                  {{ document.year }} {{ document.month }} ({{ document.shift }})
                {% elif document.month %}
                  {{ document.year }} {{ document.month }}
                {% elif document.shift %}
                  {{ document.year }} ({{ document.shift }})
                {% else %}
                  {{ document.year }}
                {% endif %}
              </div>
              <div class="document-meta">
                Uploaded on {{ document.date_created.strftime('%d %b %Y') }}
              </div>
            </div>
            <div class="document-actions">
              <a href="/pyqs_admin/documents/{{ document.id }}/view" class="action-btn view-btn">
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M8 1a2.5 2.5 0 0 1 2.5 2.5V4h-5v-.5A2.5 2.5 0 0 1 8 1zm3.5 3v-.5a3.5 3.5 0 1 0-7 0V4H1v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V4h-3.5zM2 5h12v9a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V5z"/>
                </svg>
                View PDF
              </a>

              {% if not document.has_solutions %}
                <button class="action-btn extract-btn" onclick="extractQuestions({{ document.id }})">
                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M5.255 5.786a.237.237 0 0 0 .241.247h.825c.138 0 .248-.113.266-.25.09-.656.54-1.134 1.342-1.134.686 0 1.314.343 1.314 1.168 0 .635-.374.927-.965 1.371-.673.489-1.206 1.06-1.168 1.987l.003.217a.25.25 0 0 0 .25.246h.811a.25.25 0 0 0 .25-.25v-.105c0-.718.273-.927 1.01-1.486.609-.463 1.244-.977 1.244-2.056 0-1.511-1.276-2.241-2.673-2.241-1.267 0-2.655.59-2.75 2.286zm1.557 5.763c0 .533.425.927 1.01.927.609 0 1.028-.394 1.028-.927 0-.552-.42-.94-1.029-.94-.584 0-1.009.388-1.009.94z"/>
                  </svg>
                  Extract Questions
                </button>
              {% else %}
                <a href="/pyqs_admin/documents/{{ document.id }}/edit-questions" class="action-btn edit-btn">
                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293l6.5-6.5zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/>
                  </svg>
                  Edit Questions
                </a>

                <button class="action-btn" style="background-color: #8b5cf6; color: white; border: 1px solid #8b5cf6;" onclick="generateSolutions({{ document.id }})" onmouseover="this.style.backgroundColor='#7c3aed'; this.style.borderColor='#7c3aed';" onmouseout="this.style.backgroundColor='#8b5cf6'; this.style.borderColor='#8b5cf6';">
                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                    <path d="M5.255 5.786a.237.237 0 0 0 .241.247h.825c.138 0 .248-.113.266-.25.09-.656.54-1.134 1.342-1.134.686 0 1.314.343 1.314 1.168 0 .635-.374.927-.965 1.371-.673.489-1.206 1.06-1.168 1.987l.003.217a.25.25 0 0 0 .25.246h.811a.25.25 0 0 0 .25-.25v-.105c0-.718.273-.927 1.01-1.486.609-.463 1.244-.977 1.244-2.056 0-1.511-1.276-2.241-2.673-2.241-1.267 0-2.655.59-2.75 2.286zm1.557 5.763c0 .533.425.927 1.01.927.609 0 1.028-.394 1.028-.927 0-.552-.42-.94-1.029-.94-.584 0-1.009.388-1.009.94z"/>
                  </svg>
                  Get Solutions
                </button>
              {% endif %}

              <button class="action-btn delete-btn" onclick="deleteDocument({{ document.id }})">
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
                  <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                </svg>
                Delete
              </button>
            </div>
          </div>
        </div>
      {% endfor %}

      {% if pages > 1 %}
        <div class="pagination">
          <div class="pagination-item">
            <a href="?page=1&limit={{ limit }}" class="pagination-link {% if page == 1 %}disabled{% endif %}">First</a>
          </div>
          <div class="pagination-item">
            <a href="?page={{ page - 1 }}&limit={{ limit }}" class="pagination-link {% if page == 1 %}disabled{% endif %}">Previous</a>
          </div>

          {% for p in range(max(1, page - 2), min(pages + 1, page + 3)) %}
            <div class="pagination-item">
              <a href="?page={{ p }}&limit={{ limit }}" class="pagination-link {% if p == page %}active{% endif %}">{{ p }}</a>
            </div>
          {% endfor %}

          <div class="pagination-item">
            <a href="?page={{ page + 1 }}&limit={{ limit }}" class="pagination-link {% if page == pages %}disabled{% endif %}">Next</a>
          </div>
          <div class="pagination-item">
            <a href="?page={{ pages }}&limit={{ limit }}" class="pagination-link {% if page == pages %}disabled{% endif %}">Last</a>
          </div>
        </div>
      {% endif %}
    {% else %}
      <div class="no-documents">
        No documents uploaded yet.
      </div>
    {% endif %}
  </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Add modal for extraction progress -->
<div id="extraction-modal" class="modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.5);">
  <div class="modal-content" style="background-color: white; margin: 5% auto; padding: 0; width: 90%; max-width: 600px; border-radius: 12px; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);">
    <div style="padding: 32px; text-align: center; border-bottom: 1px solid #e5e7eb;">
      <h2 id="modal-title" style="margin: 0 0 24px 0; font-size: 1.25rem; font-weight: 600; color: #111827;">Extracting Content</h2>
      <div id="loaderContainer" style="margin: 20px 0;">
        <div class="loader-wrapper">
          <div class="loader"></div>
          <div class="loader-text">
            <div id="loaderTitle">Processing...</div>
            <div id="loaderSubtitle">Please wait while we process your request</div>
            <div class="progress-bar-container" style="display: none;">
              <div class="progress-bar">
                <div id="progressBarFill" class="progress-bar-fill"></div>
              </div>
              <div id="progressText" class="progress-text">0%</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div id="logContainer" style="padding: 20px;">
      <h3>Progress Log: <span id="timer" style="color: #10b981; font-weight: bold;">00:00</span></h3>
      <div id="progressLog" class="progress-log"></div>
    </div>
  </div>
</div>

<!-- Add modal for question extraction input -->
<div id="question-input-modal" class="modal" style="display: none; position: fixed; z-index: 1001; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.5);">
  <div class="modal-content" style="background-color: white; margin: 15% auto; padding: 32px; width: 90%; max-width: 480px; border-radius: 12px; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);">
    <h2 style="margin: 0 0 24px 0; font-size: 1.25rem; font-weight: 600; color: #111827; text-align: center;">Extract Questions</h2>
    <div style="margin-bottom: 24px;">
      <label for="total-questions-input" style="display: block; margin-bottom: 8px; font-weight: 500; font-size: 0.875rem; color: #374151;">Total Number of Questions:</label>
      <input type="number" id="total-questions-input" min="1" max="500" value="50" style="width: 93%; padding: 12px 16px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 0.875rem; background-color: #f9fafb;" placeholder="Enter total number of questions">
      <div style="margin-top: 8px; font-size: 0.75rem; color: #6b7280;">Enter the total number of questions in this document (1-500)</div>
    </div>
    <div style="display: flex; gap: 12px; justify-content: flex-end;">
      <button onclick="closeQuestionInputModal()" style="padding: 12px 20px; border: 1px solid #d1d5db; border-radius: 8px; background-color: white; color: #374151; font-weight: 600; font-size: 0.875rem; cursor: pointer;">Cancel</button>
      <button onclick="startQuestionExtraction()" style="padding: 12px 20px; border: none; border-radius: 8px; background-color: #10b981; color: white; font-weight: 600; font-size: 0.875rem; cursor: pointer;">Extract Questions</button>
    </div>
  </div>
</div>

<script>
  // Track start time for elapsed time calculation
  let startTime = null;
  let timerInterval = null;

  // Function to add log entries with styling
  function addLogEntry(message, type = 'info') {
    const progressLog = document.getElementById('progressLog');
    const entry = document.createElement('div');
    entry.className = 'log-entry';

    // Add timestamp
    const now = new Date();
    const timestamp = now.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
    entry.setAttribute('data-time', timestamp);

    // Set message content
    entry.textContent = message;

    // Add specific styling based on message content or type
    if (message.toLowerCase().includes('error') || type === 'error') {
      entry.style.backgroundColor = '#f8d7da';
      entry.style.color = '#721c24';
      entry.style.borderLeftColor = '#dc3545';
    } else if (message.toLowerCase().includes('completed') || message.toLowerCase().includes('success') || type === 'success') {
      entry.style.backgroundColor = '#d4edda';
      entry.style.color = '#155724';
      entry.style.borderLeftColor = '#28a745';
    } else if (message.toLowerCase().includes('starting') || message.toLowerCase().includes('initializing') || type === 'start') {
      entry.style.backgroundColor = '#d1ecf1';
      entry.style.color = '#0c5460';
      entry.style.borderLeftColor = '#17a2b8';
    } else if (message.toLowerCase().includes('status:') || message.toLowerCase().includes('checking') || type === 'status') {
      entry.style.backgroundColor = '#fff3cd';
      entry.style.color = '#856404';
      entry.style.borderLeftColor = '#ffc107';
    } else if (message.toLowerCase().includes('task started') || type === 'task') {
      entry.style.backgroundColor = '#e2e3e5';
      entry.style.color = '#383d41';
      entry.style.borderLeftColor = '#6c757d';
      entry.style.fontWeight = '500';
    }

    // Add entry with animation
    entry.style.opacity = '0';
    entry.style.transform = 'translateX(-10px)';
    progressLog.appendChild(entry);

    // Trigger animation
    setTimeout(() => {
      entry.style.opacity = '1';
      entry.style.transform = 'translateX(0)';
      entry.style.transition = 'all 0.3s ease-out';
    }, 10);

    // Auto-scroll to bottom
    progressLog.scrollTop = progressLog.scrollHeight;
  }

  // Function to update loader
  function updateLoader(title, subtitle, progress) {
    document.getElementById('loaderTitle').textContent = title;
    document.getElementById('loaderSubtitle').textContent = subtitle;
    if (progress !== null) {
      document.getElementById('progressBarFill').style.width = progress + '%';
      document.getElementById('progressText').textContent = progress + '%';
    }
  }

  // Function to show the extraction modal
  function showExtractionModal(title) {
    document.getElementById('modal-title').textContent = title;
    document.getElementById('progressLog').innerHTML = '';

    // Reset timer
    resetTimer();
    startTimer();

    // Show loader
    updateLoader('Initializing...', 'Preparing to process your request', 0);

    document.getElementById('extraction-modal').style.display = 'block';

    // Add initial log entry
    addLogEntry(`Starting ${title.toLowerCase()}...`, 'start');
  }

  // Timer functionality
  function startTimer() {
    startTime = new Date();
    timerInterval = setInterval(updateTimer, 1000);
  }

  function updateTimer() {
    if (startTime) {
      const now = new Date();
      const elapsed = Math.floor((now - startTime) / 1000);
      const minutes = Math.floor(elapsed / 60);
      const seconds = elapsed % 60;
      const timerElement = document.getElementById('timer');
      if (timerElement) {
        timerElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
      }
    }
  }

  function stopTimer() {
    if (timerInterval) {
      clearInterval(timerInterval);
      timerInterval = null;
    }
    if (startTime) {
      const now = new Date();
      const elapsed = Math.floor((now - startTime) / 1000);
      const minutes = Math.floor(elapsed / 60);
      const seconds = elapsed % 60;
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return '00:00';
  }

  function resetTimer() {
    stopTimer();
    startTime = null;
    const timerElement = document.getElementById('timer');
    if (timerElement) {
      timerElement.textContent = '00:00';
    }
  }

  // Function to hide the extraction modal
  function hideExtractionModal() {
    document.getElementById('extraction-modal').style.display = 'none';

    // Stop timer
    const finalTime = stopTimer();

    // Log for debugging
    console.log('Modal hidden after:', finalTime);

    return finalTime;
  }

  // Function to update progress
  function updateProgress(percent, message) {
    // Update loader if provided
    if (message) {
      updateLoader('Processing...', message, percent);
    }

    // Add log entry if message provided
    if (message) {
      addLogEntry(message, 'status');
    }

    // Log for debugging
    console.log(`Progress update: ${percent}%, message: ${message || 'none'}`);
  }

  // Function to extract content with progress tracking
  function extractContent(documentId) {
    if (confirm('Are you sure you want to extract HTML content from this document?')) {
      // Show the extraction modal
      showExtractionModal('Extracting HTML Content');

      addLogEntry('Initializing HTML content extraction...', 'start');

      // Start the extraction process
      fetch(`/pyqs_admin/documents/${documentId}/extract-content`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      .then(response => {
        addLogEntry('Received response from server...', 'status');

        // Check if the response is ok (status in the range 200-299)
        if (!response.ok) {
          // If not, throw an error with the status
          return response.json().then(data => {
            throw new Error(data.error || `HTTP error! Status: ${response.status}`);
          });
        }
        // Process the response
        return response.json();
      })
      .then(data => {
        console.log('Extraction response:', data);

        const finalTime = hideExtractionModal();

        if (data.success) {
          addLogEntry(`HTML content extracted successfully! (Completed in ${finalTime})`, 'success');

          // Show success message
          alert('HTML content extracted successfully!');

          // Reload the page
          location.reload();
        } else {
          addLogEntry(`Error: ${data.error || 'Unknown error'}`, 'error');
          alert(`Error: ${data.error || 'Unknown error'}`);
        }
      })
      .catch(error => {
        console.error('Extraction error:', error);
        const finalTime = hideExtractionModal();

        const errorMessage = error.message || 'An unknown error occurred';
        addLogEntry(`Error extracting HTML content: ${errorMessage} (Failed after ${finalTime})`, 'error');
        alert(`Error extracting HTML content: ${errorMessage}`);
      });
    }
  }

  // Variable to store the current document ID for question extraction
  let currentDocumentId = null;

  function extractQuestions(documentId) {
    // Store the document ID for later use
    currentDocumentId = documentId;

    // Show the question input modal
    document.getElementById('question-input-modal').style.display = 'block';

    // Focus on the input field
    document.getElementById('total-questions-input').focus();
  }

  function closeQuestionInputModal() {
    document.getElementById('question-input-modal').style.display = 'none';
  }

  function startQuestionExtraction() {
    const totalQuestions = document.getElementById('total-questions-input').value;

    // Validate input
    if (!totalQuestions || totalQuestions < 1 || totalQuestions > 500) {
      alert('Please enter a valid number of questions (1-500)');
      return;
    }

    // Close the input modal
    closeQuestionInputModal();

    // Show the extraction modal
    showExtractionModal('Extracting Questions');

    // Create JSON payload
    const requestData = {
      document_id: currentDocumentId,
      total_questions: parseInt(totalQuestions)
    };

    addLogEntry(`Extracting ${totalQuestions} questions from document ID: ${currentDocumentId}`, 'start');
    console.log(requestData)

    // Start the extraction process
    fetch(`/pyqs_admin/documents/extract-questions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    })
    .then(response => {
      addLogEntry('Received response from extraction API...', 'status');

      // Check if the response is ok (status in the range 200-299)
      if (!response.ok) {
        // If not, throw an error with the status
        return response.json().then(data => {
          throw new Error(data.error || `HTTP error! Status: ${response.status}`);
        });
      }
      // Process the response
      return response.json();
    })
    .then(data => {
      console.log('Question extraction response:', data);

      if (data.success) {
        addLogEntry(`Task started with ID: ${data.task_id}`, 'task');
        addLogEntry('Starting status polling...', 'status');

        // Start polling for status
        pollExtractionStatus(data.task_id);
      } else {
        const finalTime = hideExtractionModal();
        addLogEntry(`Error: ${data.error || 'Unknown error'} (Failed after ${finalTime})`, 'error');
        alert(`Error: ${data.error || 'Unknown error'}`);
      }
    })
    .catch(error => {
      console.error('Question extraction error:', error);
      const finalTime = hideExtractionModal();

      const errorMessage = error.message || 'An unknown error occurred';
      addLogEntry(`Error extracting questions: ${errorMessage} (Failed after ${finalTime})`, 'error');
      alert(`Error extracting questions: ${errorMessage}`);
    });
  }

  function pollExtractionStatus(taskId) {
    let pollCount = 0;
    const maxPolls = 360; // Maximum 30 minutes (360 * 5 seconds)

    const pollInterval = setInterval(() => {
      pollCount++;
      addLogEntry(`Checking status...`, 'status');

      fetch(`/pyqs_admin/documents/extraction-status/${taskId}`)
        .then(response => response.json())
        .then(data => {
          console.log('Status poll response:', data);

          if (data.status === 'COMPLETED') {
            clearInterval(pollInterval);
            const finalTime = hideExtractionModal();

            // Check if this was a solution generation task based on modal title
            const modalTitle = document.getElementById('modal-title').textContent;
            if (modalTitle === 'Generating Solutions') {
              addLogEntry(`Solutions generated successfully! (Completed in ${finalTime})`, 'success');
              alert('Solutions generated successfully!');
            } else {
              addLogEntry(`Questions extracted successfully! (Completed in ${finalTime})`, 'success');
              alert('Questions extracted successfully!');
            }
            location.reload();
          } else if (data.status === 'FAILED') {
            clearInterval(pollInterval);
            const finalTime = hideExtractionModal();

            // Check if this was a solution generation task based on modal title
            const modalTitle = document.getElementById('modal-title').textContent;
            const errorMsg = data.error_message || 'Unknown error';
            if (modalTitle === 'Generating Solutions') {
              addLogEntry(`Error generating solutions: ${errorMsg} (Failed after ${finalTime})`, 'error');
              alert(`Error generating solutions: ${errorMsg}`);
            } else {
              addLogEntry(`Error extracting questions: ${errorMsg} (Failed after ${finalTime})`, 'error');
              alert(`Error extracting questions: ${errorMsg}`);
            }
          } else if (data.status === 'IN_PROGRESS') {
            // Update progress text based on task type
            const modalTitle = document.getElementById('modal-title').textContent;
            if (modalTitle === 'Generating Solutions') {
              updateProgress(null, 'Generating solutions...');
            } else {
              updateProgress(null, 'Processing questions...');
            }
          } else {
            // Other status updates
            addLogEntry(`Status: ${data.status} - ${data.message || 'Processing...'}`, 'status');
          }
        })
        .catch(error => {
          console.error('Status poll error:', error);
          addLogEntry(`Error checking status: ${error.message}`, 'error');

          // Continue polling on error (might be temporary network issue)
          if (pollCount >= maxPolls) {
            clearInterval(pollInterval);
            const finalTime = hideExtractionModal();
            addLogEntry(`Polling failed after ${maxPolls} attempts (Failed after ${finalTime})`, 'error');
            alert('Error checking extraction status - polling timeout');
          }
        });

      // Check if we've exceeded max polls
      if (pollCount >= maxPolls) {
        clearInterval(pollInterval);
        const finalTime = hideExtractionModal();
        addLogEntry(`Extraction timed out after ${maxPolls * 5 / 60} minutes (Timed out after ${finalTime})`, 'error');
        alert('Extraction is taking longer than expected. Please check back later.');
      }
    }, 5000); // Poll every 5 seconds
  }

  function generateSolutions(documentId) {
    if (confirm('Are you sure you want to generate solutions for this document? This will create solution files for all questions.')) {
      // Show the extraction modal
      showExtractionModal('Generating Solutions');

      addLogEntry(`Starting solution generation for document ID: ${documentId}`, 'start');

      // Start the solution generation process
      fetch(`/pyqs_admin/documents/${documentId}/generate-solutions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      .then(response => {
        addLogEntry('Received response from solution generation API...', 'status');

        // Check if the response is ok (status in the range 200-299)
        if (!response.ok) {
          // If not, throw an error with the status
          return response.json().then(data => {
            throw new Error(data.error || `HTTP error! Status: ${response.status}`);
          });
        }
        // Process the response
        return response.json();
      })
      .then(data => {
        console.log('Solution generation response:', data);

        if (data.success) {
          addLogEntry(`Task started with ID: ${data.task_id}`, 'task');
          addLogEntry('Starting status polling for solution generation...', 'status');

          // Start polling for status
          pollExtractionStatus(data.task_id);
        } else {
          const finalTime = hideExtractionModal();
          addLogEntry(`Error: ${data.error || 'Unknown error'} (Failed after ${finalTime})`, 'error');
          alert(`Error: ${data.error || 'Unknown error'}`);
        }
      })
      .catch(error => {
        console.error('Solution generation error:', error);
        const finalTime = hideExtractionModal();

        const errorMessage = error.message || 'An unknown error occurred';
        addLogEntry(`Error generating solutions: ${errorMessage} (Failed after ${finalTime})`, 'error');
        alert(`Error generating solutions: ${errorMessage}`);
      });
    }
  }

  function deleteDocument(documentId) {
    if (confirm('Are you sure you want to delete this document? This will permanently delete the document, extracted content, and all questions.')) {
      fetch(`/pyqs_admin/documents/${documentId}/delete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          alert('Document deleted successfully!');
          location.reload();
        } else {
          alert(`Error: ${data.error || 'Unknown error'}`);
        }
      })
      .catch(error => {
        alert(`Error: ${error.message}`);
      });
    }
  }

  // Set current year as default
  document.addEventListener('DOMContentLoaded', function() {
    const yearInput = document.getElementById('year');
    if (yearInput && !yearInput.value) {
      yearInput.value = new Date().getFullYear();
    }

    // Add event listener for Enter key in question input modal
    const totalQuestionsInput = document.getElementById('total-questions-input');
    if (totalQuestionsInput) {
      totalQuestionsInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          startQuestionExtraction();
        }
      });
    }

    // Close modal when clicking outside of it
    document.getElementById('question-input-modal').addEventListener('click', function(e) {
      if (e.target === this) {
        closeQuestionInputModal();
      }
    });
  });
</script>
{% endblock %}
