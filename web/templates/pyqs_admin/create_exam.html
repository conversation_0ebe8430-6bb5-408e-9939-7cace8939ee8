{% extends "base.html" %}

{% block title %}{% if is_edit %}Edit{% else %}Create{% endif %} Exam | PYQs Admin | GPT Sir{% endblock %}

{% block head %}
<style>
  .create-exam-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
  }

  .create-exam-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
  }

  .back-btn {
    background-color: var(--beige);
    color: var(--gunmetal);
    padding: 8px 16px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s;
  }

  .back-btn:hover {
    background-color: #e5e0c5;
  }

  .create-exam-form {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 30px;
  }

  .form-group {
    margin-bottom: 25px;
  }

  .form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--gunmetal);
  }

  .form-control {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s;
  }

  .form-control:focus {
    border-color: var(--tea-green);
    outline: none;
  }

  .form-row {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
  }

  .submit-btn {
    background-color: var(--tea-green);
    color: var(--gunmetal);
    border: none;
    padding: 12px 25px;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .submit-btn:hover {
    background-color: #b5e0c0;
  }

  .error-message {
    background-color: #ffebee;
    color: #c62828;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
    border-left: 4px solid #c62828;
    font-weight: 500;
    display: flex;
    align-items: center;
  }

  .error-message::before {
    content: '⚠️';
    margin-right: 10px;
    font-size: 1.2rem;
  }

  .field-hint {
    font-size: 0.85rem;
    color: #666;
    margin-top: 5px;
  }

  /* Textarea styles */
  .syllabus-textarea {
    width: 100%;
    min-height: 200px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 1rem;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    line-height: 1.5;
    resize: vertical;
    transition: border-color 0.3s, box-shadow 0.3s;
    background-color: #fafafa;
  }

  .syllabus-textarea:focus {
    border-color: var(--tea-green);
    outline: none;
    box-shadow: 0 0 0 3px rgba(181, 224, 192, 0.2);
    background-color: white;
  }

  .syllabus-textarea::placeholder {
    color: #999;
    font-style: italic;
  }

  /* Loading spinner */
  .loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--tea-green);
    animation: spin 1s ease-in-out infinite;
    margin-left: 10px;
  }

  @keyframes spin {
    to { transform: rotate(360deg); }
  }

  .hidden {
    display: none;
  }
</style>

{% endblock %}

{% block content %}
<div class="create-exam-container">
  <div class="create-exam-header">
    <h1>{% if is_edit %}Edit{% else %}Create{% endif %} Exam</h1>
    <a href="/pyqs_admin/exams" class="back-btn">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
        <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z"/>
      </svg>
      Back to Exams
    </a>
  </div>

  {% if error %}
    <div class="error-message">
      {{ error }}
    </div>
  {% endif %}

  <form method="post" class="create-exam-form">
    <div class="form-row">
      <div class="form-group">
        <label for="level" id="level-label">Level *</label>
        <select id="level" name="level" class="form-control" required onchange="javascript:getSyllabus(this.value)">
          <option value="">Select a level</option>
          {% for level in levels %}
            <option value="{{ level.name }}" {% if is_edit and exam.level == level.name %}selected{% endif %}>{{ level.name }}</option>
          {% endfor %}
        </select>
      </div>

      <div class="form-group">
        <label for="syllabus" id="syllabus-label">Syllabus *</label>
        <select id="syllabus" name="syllabus" class="form-control" required {% if not is_edit %}disabled{% endif %} onchange="javascript:getGrade(this.value)" style="{% if not is_edit %}display: none;{% endif %}">
          <option value="">Select a syllabus</option>
          {% if is_edit and syllabi %}
            {% for syllabus_item in syllabi %}
              <option value="{{ syllabus_item.syllabus }}" {% if exam.syllabus == syllabus_item.syllabus %}selected{% endif %}>{{ syllabus_item.syllabus }}</option>
            {% endfor %}
          {% endif %}
        </select>
        <div id="syllabus-loading" class="loading hidden"></div>
      </div>
    </div>

    <div class="form-row">
      <div class="form-group">
        <label for="grade" id="grade-label">Grade *</label>
        <select id="grade" name="grade" class="form-control" required {% if not is_edit %}disabled{% endif %} onchange="javascript:getSubject(this.value)" style="{% if not is_edit %}display: none;{% endif %}">
          <option value="">Select a grade</option>
          {% if is_edit and grades %}
            {% for grade_item in grades %}
              <option value="{{ grade_item.grade }}" {% if exam.grade == grade_item.grade %}selected{% endif %}>{{ grade_item.grade }}</option>
            {% endfor %}
          {% endif %}
        </select>
        <div id="grade-loading" class="loading hidden"></div>
      </div>

      <div class="form-group">
        <label for="subject" id="subject-label">Subject *</label>
        <select id="subject" name="subject" class="form-control" required {% if not is_edit %}disabled{% endif %} style="{% if not is_edit %}display: none;{% endif %}">
          <option value="">Select a subject</option>
          {% if is_edit and subjects %}
            {% for subject_item in subjects %}
              <option value="{{ subject_item.subject }}" {% if exam.subject == subject_item.subject %}selected{% endif %}>{{ subject_item.subject }}</option>
            {% endfor %}
          {% endif %}
        </select>
        <div id="subject-loading" class="loading hidden"></div>
      </div>
    </div>

    <div class="form-group">
      <label for="exam_name">Exam Name *</label>
      <input type="text" id="exam_name" name="exam_name" class="form-control" required value="{{ exam.exam_name if is_edit else '' }}">
    </div>

    <div class="form-group">
      <label for="syllabus_text">Syllabus Text</label>
      <textarea id="syllabus_text" name="syllabus_text" class="syllabus-textarea" placeholder="Enter syllabus text here...">{{ exam.syllabus_text if is_edit else '' }}</textarea>
      <div class="field-hint">You can enter detailed syllabus information here. This field supports plain text formatting.</div>
    </div>

    <div class="form-actions">
      <button type="submit" class="submit-btn">{% if is_edit %}Update{% else %}Create{% endif %} Exam</button>
    </div>
  </form>
</div>
{% endblock %}

{% block scripts %}
<script>
  var syllabusData = [];

  // Function to fetch data from our internal API
  async function fetchInternalData(url) {
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return data.results || data;
    } catch (error) {
      console.error('Error fetching data:', error);
      return null;
    }
  }

  // Function to show/hide loading indicator
  function showLoading(elementId, show) {
    const element = document.getElementById(elementId);
    if (element) {
      if (show) {
        element.classList.remove('hidden');
      } else {
        element.classList.add('hidden');
      }
    }
  }

  // Get syllabus based on level selection
  function getSyllabus(level) {
    if (!level || document.getElementById('level').selectedIndex == 0) {
      document.getElementById('syllabus').style.display = 'none';
      document.getElementById('grade').style.display = 'none';
      document.getElementById('subject').style.display = 'none';
      return;
    }

    showLoading('syllabus-loading', true);

    const url = `/pyqs_admin/api/syllabi/${encodeURIComponent(level)}`;

    fetchInternalData(url).then(data => {
      showLoading('syllabus-loading', false);

      if (data) {
        syllabusData = data;
        const select = document.getElementById('syllabus');
        select.options.length = 1; // Keep only the first option

        data.forEach(item => {
          const option = document.createElement('option');
          option.textContent = item.syllabus;
          option.value = item.syllabus;
          select.appendChild(option);
        });

        select.style.display = 'block';
        select.disabled = false;
        select.focus();
      }
    });
  }

  // Get grade based on syllabus selection
  function getGrade(syllabus) {
    if (!syllabus || document.getElementById('syllabus').selectedIndex == 0) {
      document.getElementById('grade').style.display = 'none';
      document.getElementById('subject').style.display = 'none';
      return;
    }

    showLoading('grade-loading', true);

    var length = 13;
    var level = document.getElementById('level');
    var select = document.getElementById('grade');

    if (level.value === 'School' && syllabus !== 'NIOS') {
      // For School level, generate grades 1-12
      select.options.length = 1;

      // Note: In the original code there was a condition for siteId=5 to add 'Pre Primary'
      // and set length=9, but we don't have access to session here, so using default length=13

      for (var i = 1; i < length; i++) {
        var el = document.createElement('option');
        el.textContent = i;
        el.value = i;
        select.appendChild(el);
      }

      select.focus();
      select.style.display = 'block';
      select.disabled = false;
      showLoading('grade-loading', false);
    } else {
      var seperate = true;

      // Check if syllabus is semester-based
      for (var i = 0; i < syllabusData.length; i++) {
        if (syllabus === syllabusData[i].syllabus) {
          if (syllabusData[i].grade_type === 'Semester') {
            seperate = false;
            select.options.length = 1;

            var startSemester = 1;
            var endSemester = 8;
            if (syllabusData[i].start_semester != null) startSemester = syllabusData[i].start_semester;
            if (syllabusData[i].end_semester != null) endSemester = syllabusData[i].end_semester;

            var el = document.createElement('option');
            el.textContent = 'All Semesters';
            el.value = 'All Semesters';
            select.appendChild(el);

            for (var j = startSemester; j < (1 + endSemester); j++) {
              el = document.createElement('option');
              el.textContent = 'Semester ' + j;
              el.value = 'Semester ' + j;
              select.appendChild(el);
            }

            select.focus();
            select.style.display = 'block';
            select.disabled = false;
            showLoading('grade-loading', false);
          }
          break;
        }
      }


      if (seperate) {
        if (syllabus === 'NIOS') {
          // Special handling for NIOS
          select.innerHTML = " <option>Select</option>" +
            "<option value=\"Open Basic Education\">Open Basic Education</option>\n" +
            "    <option value=\"Secondary courses\">Secondary courses</option>\n" +
            "    <option value=\"Senior Secondary courses\">Senior Secondary courses</option>\n" +
            "    <option value=\"Vocational Courses\">Vocational Courses</option>\n" +
            "    <option value=\"Diploma in Elementary Education (D.El.Ed)\">Diploma in Elementary Education (D.El.Ed)</option>" +
            "    <option value=\"5\">5</option>" +
            "    <option value=\"6\">6</option>" +
            "    <option value=\"7\">7</option>" +
            "    <option value=\"8\">8</option>" +
            "    <option value=\"9\">9</option>" +
            "    <option value=\"10\">10</option>" +
            "    <option value=\"11\">11</option>" +
            "    <option value=\"12\">12</option>";
          select.style.display = 'block';
          select.disabled = false;
          select.focus();
          showLoading('grade-loading', false);
        } else {
          // Fetch grades from API for other syllabi
          syllabus = encodeURIComponent(syllabus);
          const url = `/pyqs_admin/api/grades/${syllabus}`;

          fetchInternalData(url).then(data => {
            showLoading('grade-loading', false);

            if (data) {
              select.options.length = 1;

              data.forEach(item => {
                const option = document.createElement('option');
                option.textContent = item.grade + (item.state ? ` ( ${item.state} )` : '');
                option.value = item.grade;
                select.appendChild(option);
              });

              select.style.display = 'block';
              select.disabled = false;
              select.focus();
            }
          });
        }
      }
    }
  }

  // Get subject based on grade selection
  function getSubject(value) {
    if (!value || document.getElementById('grade').selectedIndex == 0) {
      document.getElementById('subject').style.display = 'none';
      return;
    }

    const level = document.getElementById('level');
    const syllabusSelect = document.getElementById('syllabus');
    let syllabus = syllabusSelect.value;

    if (level.value === 'School') {
      syllabus = 'School';
    }

    showLoading('subject-loading', true);

    const url = `/pyqs_admin/api/subjects/${encodeURIComponent(syllabus)}`;

    fetchInternalData(url).then(data => {
      showLoading('subject-loading', false);

      if (data) {
        const select = document.getElementById('subject');
        select.options.length = 1;

        data.forEach(item => {
          const option = document.createElement('option');
          option.textContent = item.subject;
          option.value = item.subject;
          select.appendChild(option);
        });

        select.style.display = 'block';
        select.disabled = false;
        select.focus();
      }
    });
  }

  // Function to sort level dropdown options alphabetically
  function sortLevelDropdown() {
    const levelSelect = document.getElementById('level');
    const selectedValue = levelSelect.value; // Store current selection

    // Get all options except the first one (placeholder)
    const options = Array.from(levelSelect.options).slice(1);

    // Sort options alphabetically by text content
    options.sort((a, b) => a.text.localeCompare(b.text));

    // Clear all options except the first one
    levelSelect.options.length = 1;

    // Add sorted options back
    options.forEach(option => {
      levelSelect.appendChild(option);
    });

    // Restore selection
    levelSelect.value = selectedValue;
  }

  // Initialize dropdowns on page load for edit mode
  document.addEventListener('DOMContentLoaded', function() {
    const levelSelect = document.getElementById('level');

    // Sort the level dropdown alphabetically
    sortLevelDropdown();

    // If we're in edit mode and level is already selected, trigger the cascade
    if (levelSelect.value) {
      getSyllabus(levelSelect.value);

      // Set a timeout to allow syllabus to load, then trigger grade
      setTimeout(() => {
        const syllabusSelect = document.getElementById('syllabus');
        if (syllabusSelect.value) {
          getGrade(syllabusSelect.value);

          // Set another timeout for subject
          setTimeout(() => {
            const gradeSelect = document.getElementById('grade');
            if (gradeSelect.value) {
              getSubject(gradeSelect.value);
            }
          }, 500);
        }
      }, 500);
    }
  });
</script>
{% endblock %}
